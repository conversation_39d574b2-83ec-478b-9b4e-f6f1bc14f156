//
//  ContentView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import MapKit

struct ContentView: View {
    @StateObject private var locationManager = LocationManager()
    @State private var showingLocationAlert = false
    @State private var mapType: MKMapType = .standard

    var body: some View {
        NavigationStack {
            ZStack {
                // Main Map View
                Map(coordinateRegion: $locationManager.region,
                    showsUserLocation: true,
                    userTrackingMode: .none)
                    .ignoresSafeArea()
                    .onAppear {
                        locationManager.requestLocation()
                    }

                // Top Navigation Bar
                VStack {
                    HStack {
                        Text("Melbourne Parking")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color(.systemBackground).opacity(0.9))
                            .clipShape(RoundedRectangle(cornerRadius: 12))

                        Spacer()

                        // Location Button
                        Button(action: {
                            locationManager.requestLocation()
                        }) {
                            Image(systemName: "location.fill")
                                .font(.title3)
                                .foregroundColor(.blue)
                        }
                        .padding(12)
                        .background(Color(.systemBackground))
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                    Spacer()

                    // Bottom Controls
                    HStack {
                        Spacer()

                        VStack(spacing: 12) {
                            // Map Type Toggle
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    mapType = mapType == .standard ? .satellite : .standard
                                }
                            }) {
                                Image(systemName: mapType == .standard ? "map" : "globe.americas")
                                    .font(.title3)
                                    .foregroundColor(.blue)
                            }
                            .padding(12)
                            .background(Color(.systemBackground))
                            .clipShape(Circle())
                            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)

                            // Search Button (placeholder for future functionality)
                            Button(action: {
                                // TODO: Implement search functionality
                            }) {
                                Image(systemName: "magnifyingglass")
                                    .font(.title3)
                                    .foregroundColor(.blue)
                            }
                            .padding(12)
                            .background(Color(.systemBackground))
                            .clipShape(Circle())
                            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                        }
                        .padding(.trailing, 16)
                        .padding(.bottom, 100) // Account for safe area
                    }
                }
            }
        }
        .alert("Location Access Required", isPresented: $showingLocationAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable location access in Settings to find parking near you.")
        }
        .onChange(of: locationManager.authorizationStatus) { status in
            if status == .denied || status == .restricted {
                showingLocationAlert = true
            }
        }
    }
}

#Preview {
    ContentView()
}
